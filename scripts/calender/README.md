# Calendar Data Analyzer

日历数据分析器 - 从 JSON 文件分析日历事件数据并生成表结构

## 功能特性

- 📊 分析 JSON 数据结构
- 🗄️ 生成 SQL 表结构
- 📤 导出数据到多种格式：
  - CSV 文件
  - SQLite 数据库
  - MySQL 数据库
  - 按事件类型分组的 CSV 文件
- 📝 生成详细的数据分析报告

## 使用方法

### 1. 快速运行（推荐）

```bash
# 基本分析和导出
./run.sh

# 包含MySQL导出
./run.sh --mysql
```

### 2. 使用 uv 管理项目

```bash
# 安装依赖
uv sync

# 基本分析
uv run python analyze_calendar.py

# 包含MySQL导出
uv run python analyze_calendar.py --mysql
```

### 3. 直接运行

```bash
# 基本分析
python3 analyze_calendar.py

# 包含MySQL导出
python3 analyze_calendar.py --mysql
```

### 4. 仅 MySQL 导出

```bash
# 交互式MySQL导出
python3 export_to_mysql.py

# 使用配置文件导出（推荐）
python3 export_mysql_with_config.py mysql_config.json
```

### 5. 验证数据库

```bash
# 验证SQLite数据库内容
python3 verify_db.py
```

## 输出文件

运行脚本后，会在当前目录生成以下文件：

- `calendar_schema_sqlite.sql` - SQLite 表结构定义
- `calendar_schema_mysql.sql` - MySQL 表结构定义
- `analysis_report.md` - 数据分析报告
- `calendar_events.csv` - 完整数据 CSV 导出
- `calendar_events.db` - SQLite 数据库
- `calendar_*.csv` - 按事件类型分组的 CSV 文件

### MySQL 数据库要求

使用 MySQL 导出功能需要：

1. **安装 pymysql 依赖**：

   ```bash
   pip install pymysql
   # 或使用uv
   uv add pymysql
   ```

2. **MySQL 服务器配置**：

   - MySQL 5.7+ 或 MariaDB 10.2+
   - 支持 JSON 数据类型（推荐）
   - utf8mb4 字符集支持

3. **用户权限**：
   - CREATE DATABASE（如果需要创建数据库）
   - CREATE TABLE
   - INSERT, SELECT 权限

### MySQL 配置文件

为了方便重复使用，建议创建配置文件：

1. **复制示例配置**：

   ```bash
   cp mysql_config.example.json mysql_config.json
   ```

2. **编辑配置文件**：

   ```json
   {
     "host": "localhost",
     "port": 3306,
     "user": "your_username",
     "password": "your_password",
     "database": "calendar_db",
     "create_database": true
   }
   ```

3. **使用配置文件导出**：
   ```bash
   python3 export_mysql_with_config.py mysql_config.json
   ```

## 数据结构

分析的 JSON 数据包含以下字段：

- `title` - 事件标题
- `eventType` - 事件类型（annualGregorian, annualHijri, monthlyHijri, weeklyGregorian, once）
- `eventCategory` - 事件分类（holiday, importantday, fasting, islamicday）
- `year` - 年份（可为 null）
- `month` - 月份
- `day` - 日期
- `weekday` - 星期几（可为 null）
- `route` - 路由路径（可为 null）
- `params` - 参数对象（可为 null）

## 事件类型说明

- `annualGregorian` - 公历年度事件
- `annualHijri` - 伊斯兰历年度事件
- `monthlyHijri` - 伊斯兰历月度事件
- `weeklyGregorian` - 公历周度事件
- `once` - 一次性事件

## 事件分类说明

- `holiday` - 节假日
- `importantday` - 重要日子
- `fasting` - 斋戒日
- `islamicday` - 伊斯兰教日子

## 数据统计

根据分析结果，JSON 文件包含：

- **总记录数**: 116 条
- **事件类型分布**:

  - annualGregorian (公历年度): 48 条
  - once (一次性事件): 38 条
  - annualHijri (伊斯兰历年度): 25 条
  - monthlyHijri (伊斯兰历月度): 3 条
  - weeklyGregorian (公历周度): 2 条

- **事件分类分布**:
  - importantday (重要日子): 52 条
  - holiday (节假日): 42 条
  - fasting (斋戒日): 15 条
  - islamicday (伊斯兰教日子): 7 条

## 项目结构

```
scripts/calender/
├── analyze_calendar.py           # 主分析脚本
├── export_to_mysql.py            # MySQL交互式导出脚本
├── export_mysql_with_config.py   # MySQL配置文件导出脚本
├── verify_db.py                  # 数据库验证脚本
├── run.sh                        # 快速运行脚本
├── pyproject.toml                # uv项目配置
├── mysql_config.example.json     # MySQL配置文件示例
├── README.md                     # 说明文档
├── calendar_schema_sqlite.sql     # 生成的SQLite表结构
├── calendar_schema_mysql.sql      # 生成的MySQL表结构
├── analysis_report.md             # 数据分析报告
├── calendar_events.csv            # 完整数据CSV
├── calendar_events.db             # SQLite数据库
└── calendar_*.csv                # 按类型分组的CSV文件
```
