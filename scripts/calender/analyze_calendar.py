#!/usr/bin/env python3
"""
Calendar JSON Data Analyzer and Table Structure Generator
分析日历JSON数据并生成表结构
"""

import json
import sqlite3
import csv
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    print("⚠️  pymysql未安装，MySQL功能不可用。运行: pip install pymysql")


class CalendarAnalyzer:
    """日历数据分析器"""
    
    def __init__(self, json_file_path: str):
        """初始化分析器
        
        Args:
            json_file_path: JSON文件路径
        """
        self.json_file_path = json_file_path
        self.data: List[Dict[str, Any]] = []
        self.output_dir = Path(__file__).parent
        
    def load_data(self) -> None:
        """加载JSON数据"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载 {len(self.data)} 条记录")
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            raise
    
    def analyze_structure(self) -> Dict[str, Any]:
        """分析数据结构"""
        if not self.data:
            return {}
        
        # 分析字段类型和值
        field_analysis = defaultdict(lambda: {
            'type_counts': Counter(),
            'null_count': 0,
            'sample_values': set(),
            'max_length': 0
        })
        
        for record in self.data:
            for field, value in record.items():
                analysis = field_analysis[field]
                
                if value is None:
                    analysis['null_count'] += 1
                    analysis['type_counts']['NULL'] += 1
                else:
                    value_type = type(value).__name__
                    analysis['type_counts'][value_type] += 1
                    
                    # 收集样本值
                    if len(analysis['sample_values']) < 10:
                        if isinstance(value, (str, int, float, bool)):
                            analysis['sample_values'].add(str(value))
                        elif isinstance(value, dict):
                            analysis['sample_values'].add(str(value))
                    
                    # 计算最大长度
                    if isinstance(value, str):
                        analysis['max_length'] = max(analysis['max_length'], len(value))
                    elif isinstance(value, dict):
                        analysis['max_length'] = max(analysis['max_length'], len(str(value)))
        
        return dict(field_analysis)
    
    def generate_sql_schema(self, analysis: Dict[str, Any], db_type: str = "sqlite") -> str:
        """生成SQL表结构

        Args:
            analysis: 数据分析结果
            db_type: 数据库类型 ("sqlite" 或 "mysql")
        """
        if db_type == "mysql":
            sql_lines = [
                "-- Calendar Events Table Schema for MySQL",
                "-- MySQL日历事件表结构",
                "",
                "CREATE TABLE calendar_events (",
                "    id INT AUTO_INCREMENT PRIMARY KEY,"
            ]
        else:
            sql_lines = [
                "-- Calendar Events Table Schema for SQLite",
                "-- SQLite日历事件表结构",
                "",
                "CREATE TABLE calendar_events (",
                "    id INTEGER PRIMARY KEY AUTOINCREMENT,"
            ]
        
        for field, info in analysis.items():
            # 确定SQL数据类型
            type_counts = info['type_counts']
            most_common_type = type_counts.most_common(1)[0][0] if type_counts else 'str'

            if field == 'params' or most_common_type == 'dict':
                if db_type == "mysql":
                    sql_type = "JSON"  # MySQL支持JSON类型
                else:
                    sql_type = "TEXT"  # SQLite使用TEXT
                comment = "-- JSON object"
            elif most_common_type == 'int':
                if db_type == "mysql":
                    sql_type = "INT"
                else:
                    sql_type = "INTEGER"
                comment = ""
            elif most_common_type == 'str':
                max_len = info['max_length']
                if max_len > 255:
                    sql_type = "TEXT"
                else:
                    sql_type = f"VARCHAR({max(max_len + 50, 255)})"
                comment = f"-- max length: {max_len}"
            elif most_common_type == 'bool':
                if db_type == "mysql":
                    sql_type = "BOOLEAN"
                else:
                    sql_type = "BOOLEAN"
                comment = ""
            else:
                sql_type = "TEXT"
                comment = f"-- mixed types: {dict(type_counts)}"

            # 是否允许NULL
            null_allowed = "NULL" if info['null_count'] > 0 else "NOT NULL"

            sql_lines.append(f"    {field} {sql_type} {null_allowed}, {comment}")

        if db_type == "mysql":
            sql_lines.append("    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            sql_lines.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;")
        else:
            sql_lines.append("    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            sql_lines.append(");")

        sql_lines.extend([
            "",
            "-- Indexes for better performance",
            "-- 性能优化索引",
            "CREATE INDEX idx_event_type ON calendar_events(eventType);",
            "CREATE INDEX idx_event_category ON calendar_events(eventCategory);",
            "CREATE INDEX idx_date ON calendar_events(year, month, day);",
            "CREATE INDEX idx_weekday ON calendar_events(weekday);",
            ""
        ])
        
        return "\n".join(sql_lines)
    
    def generate_analysis_report(self, analysis: Dict[str, Any]) -> str:
        """生成数据分析报告"""
        report_lines = [
            "# Calendar Data Analysis Report",
            "# 日历数据分析报告",
            "",
            f"## 基本信息",
            f"- 总记录数: {len(self.data)}",
            f"- 字段数量: {len(analysis)}",
            "",
            "## 字段分析",
            ""
        ]
        
        for field, info in analysis.items():
            report_lines.extend([
                f"### {field}",
                f"- 数据类型分布: {dict(info['type_counts'])}",
                f"- NULL值数量: {info['null_count']}",
                f"- 最大长度: {info['max_length']}",
                f"- 样本值: {list(info['sample_values'])[:5]}",
                ""
            ])
        
        # 统计各种事件类型
        event_types = Counter(record.get('eventType') for record in self.data)
        event_categories = Counter(record.get('eventCategory') for record in self.data)
        
        report_lines.extend([
            "## 事件类型统计",
            "",
            "### eventType分布:",
            *[f"- {k}: {v}" for k, v in event_types.most_common()],
            "",
            "### eventCategory分布:",
            *[f"- {k}: {v}" for k, v in event_categories.most_common()],
            ""
        ])
        
        return "\n".join(report_lines)

    def export_to_csv(self) -> str:
        """导出数据到CSV文件"""
        csv_file = self.output_dir / "calendar_events.csv"

        if not self.data:
            return str(csv_file)

        # 获取所有字段名
        all_fields = set()
        for record in self.data:
            all_fields.update(record.keys())

        fieldnames = sorted(all_fields)

        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for record in self.data:
                    # 处理params字段（JSON对象）
                    row = record.copy()
                    if 'params' in row and isinstance(row['params'], dict):
                        row['params'] = json.dumps(row['params'], ensure_ascii=False)
                    writer.writerow(row)

            print(f"✅ CSV文件已导出: {csv_file}")
            return str(csv_file)
        except Exception as e:
            print(f"❌ CSV导出失败: {e}")
            return ""

    def export_to_sqlite(self, schema_sql: str) -> str:
        """导出数据到SQLite数据库"""
        db_file = self.output_dir / "calendar_events.db"

        try:
            # 删除已存在的数据库文件
            if db_file.exists():
                db_file.unlink()

            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 创建表结构
            cursor.executescript(schema_sql)

            # 插入数据
            if self.data:
                # 获取字段名（排除id和created_at）
                sample_record = self.data[0]
                fields = [k for k in sample_record.keys()]

                placeholders = ', '.join(['?' for _ in fields])
                insert_sql = f"INSERT INTO calendar_events ({', '.join(fields)}) VALUES ({placeholders})"

                for record in self.data:
                    values = []
                    for field in fields:
                        value = record.get(field)
                        if isinstance(value, dict):
                            value = json.dumps(value, ensure_ascii=False)
                        values.append(value)

                    cursor.execute(insert_sql, values)

            conn.commit()
            conn.close()

            print(f"✅ SQLite数据库已创建: {db_file}")
            return str(db_file)
        except Exception as e:
            print(f"❌ SQLite导出失败: {e}")
            return ""

    def export_separate_tables(self) -> None:
        """导出到分离的表格（按事件类型分组）"""
        if not self.data:
            return

        # 按eventType分组
        grouped_data = defaultdict(list)
        for record in self.data:
            event_type = record.get('eventType', 'unknown')
            grouped_data[event_type].append(record)

        # 为每个类型创建CSV文件
        for event_type, records in grouped_data.items():
            safe_filename = event_type.replace('/', '_').replace('\\', '_')
            csv_file = self.output_dir / f"calendar_{safe_filename}.csv"

            if records:
                all_fields = set()
                for record in records:
                    all_fields.update(record.keys())

                fieldnames = sorted(all_fields)

                try:
                    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()

                        for record in records:
                            row = record.copy()
                            if 'params' in row and isinstance(row['params'], dict):
                                row['params'] = json.dumps(row['params'], ensure_ascii=False)
                            writer.writerow(row)

                    print(f"✅ 已导出 {event_type}: {csv_file} ({len(records)} 条记录)")
                except Exception as e:
                    print(f"❌ 导出 {event_type} 失败: {e}")

    def export_to_mysql(self, mysql_config: Dict[str, str], schema_sql: str) -> bool:
        """导出数据到MySQL数据库

        Args:
            mysql_config: MySQL连接配置
            schema_sql: MySQL表结构SQL

        Returns:
            bool: 是否成功
        """
        if not MYSQL_AVAILABLE:
            print("❌ pymysql未安装，无法连接MySQL")
            return False

        try:
            import pymysql

            # 连接MySQL
            connection = pymysql.connect(
                host=mysql_config.get('host', 'localhost'),
                port=mysql_config.get('port', 3306),
                user=mysql_config['user'],
                password=mysql_config['password'],
                database=mysql_config['database'],
                charset='utf8mb4'
            )

            cursor = connection.cursor()

            # 删除已存在的表
            cursor.execute("DROP TABLE IF EXISTS calendar_events")

            # 创建表结构
            cursor.execute(schema_sql.split(';')[0] + ';')  # 只执行CREATE TABLE语句

            # 插入数据
            if self.data:
                # 获取字段名（排除id和created_at）
                sample_record = self.data[0]
                fields = [k for k in sample_record.keys()]

                placeholders = ', '.join(['%s' for _ in fields])
                insert_sql = f"INSERT INTO calendar_events ({', '.join(fields)}) VALUES ({placeholders})"

                for record in self.data:
                    values = []
                    for field in fields:
                        value = record.get(field)
                        if isinstance(value, dict):
                            value = json.dumps(value, ensure_ascii=False)
                        values.append(value)

                    cursor.execute(insert_sql, values)

            # 创建索引
            index_statements = [
                "CREATE INDEX idx_event_type ON calendar_events(eventType)",
                "CREATE INDEX idx_event_category ON calendar_events(eventCategory)",
                "CREATE INDEX idx_date ON calendar_events(year, month, day)",
                "CREATE INDEX idx_weekday ON calendar_events(weekday)"
            ]

            for index_sql in index_statements:
                try:
                    cursor.execute(index_sql)
                except Exception as e:
                    print(f"⚠️  创建索引失败: {e}")

            connection.commit()
            connection.close()

            print(f"✅ MySQL数据库导入成功: {mysql_config['host']}/{mysql_config['database']}")
            return True

        except Exception as e:
            print(f"❌ MySQL导出失败: {e}")
            return False

    def get_mysql_config(self) -> Optional[Dict[str, str]]:
        """获取MySQL配置（交互式输入）"""
        print("\n🔧 MySQL数据库配置")
        print("=" * 30)

        try:
            config = {}
            config['host'] = input("MySQL主机地址 [localhost]: ").strip() or 'localhost'

            port_input = input("MySQL端口 [3306]: ").strip()
            config['port'] = int(port_input) if port_input else 3306

            config['user'] = input("MySQL用户名: ").strip()
            if not config['user']:
                print("❌ 用户名不能为空")
                return None

            config['password'] = input("MySQL密码: ").strip()
            if not config['password']:
                print("❌ 密码不能为空")
                return None

            config['database'] = input("数据库名: ").strip()
            if not config['database']:
                print("❌ 数据库名不能为空")
                return None

            return config

        except KeyboardInterrupt:
            print("\n❌ 用户取消")
            return None
        except Exception as e:
            print(f"❌ 配置输入失败: {e}")
            return None

    def run_analysis(self, export_mysql: bool = False) -> None:
        """运行完整分析流程"""
        print("🚀 开始分析日历数据...")

        # 1. 加载数据
        self.load_data()

        # 2. 分析结构
        print("📊 分析数据结构...")
        analysis = self.analyze_structure()

        # 3. 生成SQL schema (SQLite版本)
        print("🗄️ 生成SQL表结构...")
        sqlite_schema = self.generate_sql_schema(analysis, "sqlite")
        mysql_schema = self.generate_sql_schema(analysis, "mysql")

        # 4. 生成分析报告
        print("📝 生成分析报告...")
        report = self.generate_analysis_report(analysis)

        # 5. 保存文件
        sqlite_schema_file = self.output_dir / "calendar_schema_sqlite.sql"
        mysql_schema_file = self.output_dir / "calendar_schema_mysql.sql"
        report_file = self.output_dir / "analysis_report.md"

        with open(sqlite_schema_file, 'w', encoding='utf-8') as f:
            f.write(sqlite_schema)
        print(f"✅ SQLite结构已保存: {sqlite_schema_file}")

        with open(mysql_schema_file, 'w', encoding='utf-8') as f:
            f.write(mysql_schema)
        print(f"✅ MySQL结构已保存: {mysql_schema_file}")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"✅ 分析报告已保存: {report_file}")

        # 6. 导出数据
        print("📤 导出数据...")
        self.export_to_csv()
        self.export_to_sqlite(sqlite_schema)
        self.export_separate_tables()

        # 7. MySQL导出（如果需要）
        if export_mysql:
            mysql_config = self.get_mysql_config()
            if mysql_config:
                print("📤 导出到MySQL...")
                self.export_to_mysql(mysql_config, mysql_schema)

        print("🎉 分析完成！")


def main():
    """主函数"""
    import sys

    # 检查命令行参数
    export_mysql = "--mysql" in sys.argv or "-m" in sys.argv

    # JSON文件路径（相对于脚本当前目录）
    script_dir = Path(__file__).parent
    repo_root = script_dir.parent.parent
    json_file = repo_root / "apk-res/apk-res/assets/flutter_assets/assets/data/calendar.json"

    # 检查文件是否存在
    if not json_file.exists():
        print(f"❌ 找不到JSON文件: {json_file}")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"脚本目录: {script_dir}")
        print(f"仓库根目录: {repo_root}")
        return

    # 显示帮助信息
    if "--help" in sys.argv or "-h" in sys.argv:
        print("Calendar Data Analyzer - 日历数据分析器")
        print("=" * 40)
        print("用法:")
        print("  python analyze_calendar.py           # 基本分析和导出")
        print("  python analyze_calendar.py --mysql   # 包含MySQL导出")
        print("  python analyze_calendar.py -m        # 包含MySQL导出（简写）")
        print("  python analyze_calendar.py --help    # 显示帮助")
        print("")
        print("输出文件:")
        print("  - calendar_schema_sqlite.sql  (SQLite表结构)")
        print("  - calendar_schema_mysql.sql   (MySQL表结构)")
        print("  - analysis_report.md          (数据分析报告)")
        print("  - calendar_events.csv         (完整数据CSV)")
        print("  - calendar_events.db          (SQLite数据库)")
        print("  - calendar_*.csv              (按类型分组的CSV)")
        return

    # 创建分析器并运行
    analyzer = CalendarAnalyzer(str(json_file))
    analyzer.run_analysis(export_mysql=export_mysql)


if __name__ == "__main__":
    main()
