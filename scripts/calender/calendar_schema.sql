-- Calendar Events Table Schema
-- 日历事件表结构

CREATE TABLE calendar_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL, -- max length: 97
    eventType VARCHAR(255) NOT NULL, -- max length: 15
    eventCategory VARCHAR(255) NOT NULL, -- max length: 12
    year TEXT NULL, -- mixed types: {'NULL': 78, 'int': 38}
    month INTEGER NULL, 
    day INTEGER NULL, 
    weekday TEXT NULL, -- mixed types: {'NULL': 114, 'int': 2}
    route VARCHAR(255) NULL, -- max length: 33
    params TEXT NULL, -- JSON object
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
-- 性能优化索引
CREATE INDEX idx_event_type ON calendar_events(eventType);
CREATE INDEX idx_event_category ON calendar_events(eventCategory);
CREATE INDEX idx_date ON calendar_events(year, month, day);
CREATE INDEX idx_weekday ON calendar_events(weekday);
