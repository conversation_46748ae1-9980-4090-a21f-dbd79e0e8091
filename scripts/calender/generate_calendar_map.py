import datetime
from hijri_converter import Gregorian

# --- 这是您每年唯一需要修改的地方 ---
YEAR_TO_GENERATE = 2025
# -----------------------------------------

# 伊斯兰历（Hijri）月份的通用名称
HIJRI_MONTH_NAMES = [
    "Mu<PERSON><PERSON>", "Safar", "Ra<PERSON>' al-awwal", "Ra<PERSON>' al-thani",
    "Jumada al-awwal", "Jumada al-thani", "Rajab", "Sha'ban",
    "Ramadan", "Shawwal", "Dhu al-Qi'dah", "Dhu al-Hijjah"
]

def generate_hijri_mapping(year):
    """
    为指定的公历年份生成每一天的公历-伊斯兰历映射关系。
    """
    print(f"--- {year}年 公历-伊斯兰历(Urfi) 对应表 ---")
    print("公历日期\t\t伊斯兰历日期")
    print("-------------------------------------------------")
    
    # 从该年的1月1日开始
    current_date = datetime.date(year, 1, 1)
    
    # 循环遍历该年的每一天
    while current_date.year == year:
        # 将公历日期转换为伊斯兰历日期
        gregorian_date = Gregorian(current_date.year, current_date.month, current_date.day)
        hijri_date = gregorian_date.to_hijri()
        
        # 获取伊斯兰历的年月日和月份名称
        h_year = hijri_date.year
        h_month_num = hijri_date.month
        h_day = hijri_date.day
        # 从列表中获取月份名称（列表索引从0开始，所以要减1）
        h_month_name = HIJRI_MONTH_NAMES[h_month_num - 1]
        
        # 格式化输出
        gregorian_str = current_date.strftime('%Y-%m-%d')
        hijri_str = f"{h_day} {h_month_name} {h_year} H"
        
        print(f"{gregorian_str}\t\t{hijri_str}")
        
        # 进入下一天
        current_date += datetime.timedelta(days=1)

# --- 主程序入口 ---
if __name__ == "__main__":
    generate_hijri_mapping(YEAR_TO_GENERATE)